"""
Configuration loaders for database setup.
Supports both YAML config files and environment variables.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv


class ConfigLoader:
    """Base class for configuration loaders."""

    def load_db_config(self) -> Dict[str, Any]:
        """Load database configuration. Must be implemented by subclasses."""
        raise NotImplementedError(
            "Subclasses must implement load_db_config method")


class YamlConfigLoader(ConfigLoader):
    """Load configuration from YAML file."""

    def __init__(self, config_path: str = 'config.yml'):
        self.config_path = Path(config_path)

    def load_db_config(self) -> Dict[str, Any]:
        """Load database configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(
                f"Config file not found: {self.config_path}")

        with open(self.config_path, 'r') as file:
            config = yaml.safe_load(file)

        # Load and validate required database configuration
        db_config = config.get('database', {})
        db_name = db_config.get('name')
        db_user = db_config.get('user')
        db_password = db_config.get('password')

        # Assert that required database credentials are present
        assert db_name is not None, "Database name is required but not found in config.yml"
        assert db_user is not None, "Database user is required but not found in config.yml"
        assert db_password is not None, "Database password is required but not found in config.yml"

        print(
            f"[+] Database credentials loaded from YAML for database: {db_name}")

        return {
            'host': db_config.get('host', 'localhost'),
            'port': int(db_config.get('port', 5432)),
            'database': db_name,
            'user': db_user,
            'password': db_password
        }


class EnvConfigLoader(ConfigLoader):
    """Load configuration from environment variables."""

    def __init__(self, env_file: str = '.env'):
        self.env_file = env_file
        # Load environment variables from .env file if it exists
        if Path(env_file).exists():
            load_dotenv(env_file)

    def load_db_config(self) -> Dict[str, Any]:
        """Load database configuration from environment variables."""
        # Load and validate required environment variables
        db_name = os.environ.get('DB_NAME')
        db_user = os.environ.get('DB_USER')
        db_password = os.environ.get('DB_PASSWORD')

        # Assert that required database credentials are present
        assert db_name is not None, "DB_NAME environment variable is required but not found"
        assert db_user is not None, "DB_USER environment variable is required but not found"
        assert db_password is not None, "DB_PASSWORD environment variable is required but not found"

        print(
            f"[+] Database credentials loaded from environment for database: {db_name}")

        return {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'port': int(os.environ.get('DB_PORT', 5432)),
            'database': db_name,
            'user': db_user,
            'password': db_password
        }


def get_config_loader(config_type: str = 'yaml', **kwargs) -> ConfigLoader:
    """
    Factory function to get the appropriate config loader.

    Args:
        config_type: Either 'yaml' or 'env'
        **kwargs: Additional arguments passed to the loader constructor

    Returns:
        ConfigLoader instance
    """
    if config_type.lower() == 'yaml':
        return YamlConfigLoader(**kwargs)
    elif config_type.lower() == 'env':
        return EnvConfigLoader(**kwargs)
    else:
        raise ValueError(
            f"Unsupported config type: {config_type}. Use 'yaml' or 'env'")


def get_db_config(config_type: str = 'yaml', **kwargs) -> Dict[str, Any]:
    """
    Convenience function to directly get database configuration.

    Args:
        config_type: Either 'yaml' or 'env'
        **kwargs: Additional arguments passed to the loader constructor

    Returns:
        Dictionary with database configuration
    """
    loader = get_config_loader(config_type, **kwargs)
    return loader.load_db_config()
