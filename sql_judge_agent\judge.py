# sql_evaluater/judge.py
from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI

# 1. Define the desired structured output using Pydantic
class EvaluationCriteria(BaseModel):
    score: int = Field(description="The score from 1 (poor) to 5 (excellent).")
    reasoning: str = Field(description="Detailed reasoning for the given score.")

class SecurityCheck(BaseModel):
    is_safe: bool = Field(description="True if the query is safe, False if it contains potential threats.")
    reasoning: str = Field(description="Explanation of any potential security threats like DROP, DELETE without WHERE, etc.")

class SQLEvaluation(BaseModel):
    """Structured format for the SQL query evaluation."""
    correctness: EvaluationCriteria = Field(description="Evaluation of the query's logical correctness.")
    optimization: EvaluationCriteria = Field(description="Evaluation of the query's performance and efficiency.")
    security: SecurityCheck = Field(description="Analysis of potential data loss or security threats.")
    final_verdict: str = Field(description="A final 'PASS' or 'FAIL' verdict based on the overall evaluation.")


# 2. Implement the Judge class
class GeminiSQLJudge:
    def __init__(self, config: Dict[str, Any]):
        """Initializes the Gemini Judge with a specific model and structured output."""
        self.llm = ChatGoogleGenerativeAI(
            model=config['model_name'],
            temperature=config['temperature'],
            convert_system_message_to_human=True # Important for Gemini
        )
        # Bind the Pydantic model to the LLM to enforce structured output
        self.structured_llm = self.llm.with_structured_output(SQLEvaluation)
        self.prompt_template = self._get_prompt_template()

    def _get_prompt_template(self) -> ChatPromptTemplate:
        """Creates the detailed evaluation prompt (the rubric)."""
        return ChatPromptTemplate.from_messages([
            ("system", 
             """You are an expert database administrator and senior data analyst. Your task is to meticulously evaluate a generated SQL query based on a user's request and a database schema. 
             
             You must evaluate across three criteria: Correctness, Optimization, and Security.
             Provide your evaluation in the requested structured JSON format ONLY. Do not add any other text.
             
             Evaluation Criteria Breakdown:
             1.  **Correctness (Score 1-5):**
                 - Does the query correctly answer the user's question?
                 - Does it select the right columns and join the correct tables?
                 - Are the filtering conditions (`WHERE` clauses) accurate?
             2.  **Optimization (Score 1-5):**
                 - Is the query efficient? Does it avoid unnecessary subqueries or complex operations where a simpler JOIN would suffice?
                 - Are indexes likely to be used effectively (hypothetically)?
                 - 5 is highly optimal, 1 is extremely inefficient.
             3.  **Security (is_safe: true/false):**
                 - Does the query pose a threat? Look for `DROP`, `TRUNCATE`, `DELETE` without a `WHERE` clause, or other destructive operations.
                 - Read-only `SELECT` queries are generally safe.
             """),
            ("human", 
             """
             Here is the information for your evaluation:
             
             **Database Schema:**
             ```sql
             {schema}
             ```

             **User's Original Question:**
             "{user_prompt}"

             **Generated SQL Query to Evaluate:**
             ```sql
             {generated_sql}
             ```
             
             Please provide your detailed evaluation now.
             """)
        ])

    def evaluate(self, schema: str, user_prompt: str, generated_sql: str) -> SQLEvaluation:
        """Runs the evaluation chain."""
        chain = self.prompt_template | self.structured_llm
        return chain.invoke({
            "schema": schema,
            "user_prompt": user_prompt,
            "generated_sql": generated_sql
        })