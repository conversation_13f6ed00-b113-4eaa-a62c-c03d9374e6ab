"""
Utility module for easy configuration loading throughout the project.
This module provides a clean interface to load database configuration
from anywhere in the project without complex import logic.
"""

import sys
from pathlib import Path
from typing import Dict, Any

# Ensure the root directory is in Python path
_root_dir = Path(__file__).parent.absolute()
if str(_root_dir) not in sys.path:
    sys.path.insert(0, str(_root_dir))

# Import the config loaders
from config_loaders import get_config_loader, get_db_config


def load_db_config(config_type: str = 'yaml', **kwargs) -> Dict[str, Any]:
    """
    Load database configuration from the specified source.
    
    This is a convenience function that can be imported from anywhere
    in the project to get database configuration.
    
    Args:
        config_type: Either 'yaml' or 'env'
        **kwargs: Additional arguments passed to the loader constructor
    
    Returns:
        Dictionary with database configuration
        
    Example:
        from config_utils import load_db_config
        db_config = load_db_config('yaml')
    """
    return get_db_config(config_type, **kwargs)


def get_loader(config_type: str = 'yaml', **kwargs):
    """
    Get a configuration loader instance.
    
    Args:
        config_type: Either 'yaml' or 'env'
        **kwargs: Additional arguments passed to the loader constructor
    
    Returns:
        ConfigLoader instance
        
    Example:
        from config_utils import get_loader
        loader = get_loader('yaml')
        db_config = loader.load_db_config()
    """
    return get_config_loader(config_type, **kwargs)
