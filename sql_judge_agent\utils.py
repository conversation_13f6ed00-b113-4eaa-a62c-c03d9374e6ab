# sql_evaluater/utils.py
import os
import psycopg2
from typing import Dict, Any, List


def get_db_schema(db_config: Dict[str, Any]) -> str:
    """
    Connects to a PostgreSQL database and fetches the schema of all public tables.

    Args:
        db_config: A dictionary with database connection details.

    Returns:
        A string containing formatted 'CREATE TABLE' statements for the LLM.
    """
    conn = None
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        # Get all table names in the public schema
        cursor.execute("""
            SELECT tablename 
            FROM pg_tables 
            WHERE schemaname = 'public'
        """)
        tables = [row[0] for row in cursor.fetchall()]

        schema_parts: List[str] = []
        for table_name in tables:
            # Get column definitions for each table
            cursor.execute(f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = '{table_name}'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            
            # Format as a CREATE TABLE statement
            col_defs = ",\n  ".join([f"{col_name} {data_type}" for col_name, data_type in columns])
            create_statement = f"CREATE TABLE {table_name} (\n  {col_defs}\n);"
            schema_parts.append(create_statement)

        print("✅ Successfully fetched database schema.")
        print("\n\n".join(schema_parts))
        return "\n\n".join(schema_parts)

    except psycopg2.Error as e:
        print(f"❌ Database Error: {e}")
        return ""
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    from config_loaders import get_config_loader
    config_loader = get_config_loader('yaml')
    db_config = config_loader.load_db_config()
    get_db_schema(db_config)
